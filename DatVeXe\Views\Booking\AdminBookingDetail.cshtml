@model dynamic
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Chi tiết đặt vé";
    var chuyenXe = Model.ChuyenXe;
    var choNgoi = Model.ChoNgoi;
    var thanhToans = Model.ThanhToans as IEnumerable<DatVeXe.Models.ThanhToan>;
    
    var statusClass = "";
    var statusText = "";
    var statusIcon = "";
    
    switch ((int)Model.VeTrangThai)
    {
        case 1: // Đã đặt
            statusClass = "bg-warning text-dark";
            statusText = "Chưa thanh toán";
            statusIcon = "bi-exclamation-circle";
            break;
        case 2: // Đã thanh toán
            statusClass = "bg-success";
            statusText = "Đã thanh toán";
            statusIcon = "bi-check-circle";
            break;
        case 3: // Đã sử dụng
            statusClass = "bg-info";
            statusText = "Đã sử dụng";
            statusIcon = "bi-person-check";
            break;
        case 4: // Đã hủy
            statusClass = "bg-danger";
            statusText = "Đã hủy";
            statusIcon = "bi-x-circle";
            break;
        case 5: // Đã hoàn tiền
            statusClass = "bg-secondary";
            statusText = "Đã hoàn tiền";
            statusIcon = "bi-arrow-return-left";
            break;
        case 6: // Đã hoàn thành
            statusClass = "bg-primary";
            statusText = "Đã hoàn thành";
            statusIcon = "bi-check-all";
            break;
        default:
            statusClass = "bg-warning text-dark";
            statusText = "Chưa thanh toán";
            statusIcon = "bi-exclamation-circle";
            break;
    }
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-ticket-detailed me-2"></i>Chi tiết đặt vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("AdminBookingList", "Booking")">Quản lý đặt vé</a></li>
                <li class="breadcrumb-item active">Chi tiết đặt vé</li>
            </ol>
        </div>
        <div>
            <a href="@Url.Action("PrintTicket", "Booking", new { id = Model.VeId })" class="btn btn-success" target="_blank">
                <i class="bi bi-printer me-1"></i>In vé
            </a>
            <a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-outline-secondary ms-2">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-7">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Thông tin vé</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="badge @statusClass">
                            <i class="bi @statusIcon me-1"></i>@statusText
                        </span>
                        <span class="text-muted">Mã vé: <strong>@Model.MaVe</strong></span>
                    </div>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tên khách hàng</label>
                                <p class="mb-0">@Model.TenKhach</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Số điện thoại</label>
                                <p class="mb-0">@Model.SoDienThoai</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email</label>
                                <p class="mb-0">@(string.IsNullOrEmpty(Model.Email) ? "Không có" : Model.Email)</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Ngày đặt vé</label>
                                <p class="mb-0">@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Chuyến xe</label>
                                <p class="mb-0">@(chuyenXe != null ? (chuyenXe.TuyenDuong != null ? chuyenXe.TuyenDuong.TenTuyen : ($"{chuyenXe.DiemDi} - {chuyenXe.DiemDen}")) + " (" + chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm") + ")" : "")</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Ghế</label>
                                <p class="mb-0">@Model.DanhSachGhe</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tổng tiền</label>
                                <p class="mb-0 fw-bold text-success">@Model.TongTien.ToString("#,##0") VNĐ</p>
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(Model.GhiChu))
                        {
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Ghi chú</label>
                                    <p class="mb-0">@Model.GhiChu</p>
                                </div>
                            </div>
                        }
                        @if (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaHuy && !string.IsNullOrEmpty(Model.LyDoHuy))
                        {
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-danger">Lý do hủy</label>
                                    <p class="mb-0">@Model.LyDoHuy</p>
                                </div>
                            </div>
                        }
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-gear me-1"></i>Cập nhật trạng thái
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@Model.VeId, 'DaDon'); return false;"><i class="bi bi-check2-square me-2"></i>Đã đón</a></li>
                                <li><a class="dropdown-item text-warning" href="#" onclick="updateTripStatus(@Model.VeId, 'KhongCoMat'); return false;"><i class="bi bi-x-square me-2"></i>Không có mặt</a></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="updateTripStatus(@Model.VeId, 'HuyChuyen'); return false;"><i class="bi bi-arrow-repeat me-2"></i>Huỷ chuyến</a></li>
                            </ul>
                        </div>
                        <button class="btn btn-outline-primary" id="btnSendNotification" data-id="@Model.VeId" data-name="@Model.TenKhach">
                            <i class="bi bi-envelope me-1"></i>Gửi thông báo
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-5">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-credit-card me-2"></i>Lịch sử thanh toán</h5>
                </div>
                <div class="card-body p-0">
                    @if (thanhToans != null && thanhToans.Any())
                    {
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Mã giao dịch</th>
                                        <th>Phương thức</th>
                                        <th>Số tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày thanh toán</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var tt in thanhToans)
                                    {
                                        <tr>
                                            <td>@tt.MaGiaoDich</td>
                                            <td>
                                                @switch (tt.PhuongThucThanhToan)
                                                {
                                                    case DatVeXe.Models.PhuongThucThanhToan.TienMat:
                                                        <span>Tiền mặt</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.ChuyenKhoan:
                                                        <span>Chuyển khoản</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.ThanhToanOnline:
                                                        <span>Thanh toán online</span>
                                                        break;
                                                    default:
                                                        <span>Khác</span>
                                                        break;
                                                }
                                            </td>
                                            <td>@tt.SoTien.ToString("#,##0") VNĐ</td>
                                            <td>
                                                @if (tt.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.ThanhCong)
                                                {
                                                    <span class="badge bg-success">Thành công</span>
                                                }
                                                else if (tt.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.DangXuLy)
                                                {
                                                    <span class="badge bg-warning text-dark">Đang xử lý</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Thất bại</span>
                                                }
                                            </td>
                                            <td>@tt.NgayThanhToan.ToString("dd/MM/yyyy HH:mm")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="bi bi-credit-card text-muted" style="font-size: 2rem;"></i>
                            <p class="mt-2 mb-0">Chưa có lịch sử thanh toán</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gửi thông báo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="notificationVeId" value="">
                <div class="mb-3">
                    <label for="notificationSubject" class="form-label">Tiêu đề</label>
                    <input type="text" class="form-control" id="notificationSubject" placeholder="Tiêu đề email">
                </div>
                <div class="mb-3">
                    <label for="notificationMessage" class="form-label">Nội dung</label>
                    <textarea class="form-control" id="notificationMessage" rows="4" placeholder="Nội dung thông báo..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="sendNotification">Gửi thông báo</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking').addClass('active');
            
            // Xử lý modal gửi thông báo
            $('#btnSendNotification').click(function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var name = $(this).data('name');
                
                // Get ticket info from server
                $.ajax({
                    url: '@Url.Action("GetTicketInfo", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#notificationVeId').val(veId);
                            $('#notificationSubject').val('Thông báo về vé xe của bạn');
                            $('#notificationMessage').val('Kính gửi ' + name + ',\n\nChúng tôi gửi thông báo này để...\n\nTrân trọng,\nNhà xe');
                            $('#notificationModal').modal('show');
                        } else {
                            showToast('Không thể gửi thông báo cho vé này: ' + response.message, 'warning');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi lấy thông tin vé', 'danger');
                    }
                });
            });
            
            // Xử lý gửi thông báo
            $('#sendNotification').click(function() {
                var veId = $('#notificationVeId').val();
                var subject = $('#notificationSubject').val();
                var message = $('#notificationMessage').val();
                
                $.ajax({
                    url: '@Url.Action("SendTicketNotification", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        subject: subject,
                        message: message
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('Đã gửi thông báo thành công', 'success');
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi gửi thông báo', 'danger');
                    }
                });
                
                $('#notificationModal').modal('hide');
            });
        });

        function updateTripStatus(veId, status) {
            let statusText = '';
            switch(status) {
                case 'DaDon': statusText = 'đã đón'; break;
                case 'KhongCoMat': statusText = 'không có mặt'; break;
                case 'HuyChuyen': statusText = 'huỷ chuyến'; break;
            }
            
            if (!confirm(`Xác nhận cập nhật trạng thái ${statusText} cho vé này?`)) return;
            
            fetch('@Url.Action("UpdateTripStatus", "Booking")', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `veId=${veId}&trangThai=${status}`
            })
            .then(r => r.json())
            .then(res => {
                if (res.success) {
                    showToast(res.message || 'Cập nhật trạng thái thành công', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast(res.message || 'Có lỗi xảy ra', 'danger');
                }
            })
            .catch(err => {
                showToast('Có lỗi xảy ra khi cập nhật trạng thái', 'danger');
                console.error(err);
            });
        }
        
        // Hiển thị thông báo toast
        function showToast(message, type) {
            var toast = $('<div class="toast align-items-center text-white bg-' + type + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">');
            toast.html(
                '<div class="d-flex">'+
                '  <div class="toast-body">' + message + '</div>'+
                '  <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>'+
                '</div>'
            );
            
            $('.toast-container').append(toast);
            var bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();
            
            // Xóa toast sau khi ẩn
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>
    
    <style>
        .card {
            border-radius: 0.5rem;
            overflow: hidden;
            border: none;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            border-bottom: 1px solid rgba(0,0,0,.125);
            padding: 0.75rem 1.25rem;
        }
        
        .table th, .table td {
            padding: 0.75rem 1rem;
            vertical-align: middle;
        }
        
        .table th {
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.85rem;
            padding: 0.5em 0.75em;
        }
    </style>
}
                else { <span class="text-warning">❌ Chưa thanh toán</span> }
            </td></tr>
            <tr><th>Ngày đặt</th><td>@(Model.NgayDat.ToString("dd/MM/yyyy HH:mm"))</td></tr>
            <tr><th>Ghi chú</th><td>@Model.GhiChu</td></tr>
            <tr><th>Tổng tiền</th><td>@String.Format("{0:N0}", Model.GiaVe) đ</td></tr>
            <tr><th>Phương thức thanh toán</th><td>@Model.PhuongThucThanhToan</td></tr>
            <tr><th>Thời gian thanh toán</th><td>@(Model.ThoiGianThanhToan != null ? ((DateTime)Model.ThoiGianThanhToan).ToString("dd/MM/yyyy HH:mm") : "")</td></tr>
        </table>
    </div>
    <div class="col-md-6 text-center">
        <h5>Mã QR</h5>
        <div id="qrcode"></div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js"></script>
        <script>
            var qr = new QRious({
                element: document.getElementById('qrcode'),
                value: '@Model.QRCode',
                size: 180
            });
        </script>
    </div>
</div>
@if (thanhToans != null && thanhToans.Any())
{
    <h4>Lịch sử thanh toán</h4>
    <table class="table table-sm table-bordered">
        <thead><tr><th>Phương thức</th><th>Số tiền</th><th>Thời gian</th><th>Mã giao dịch</th></tr></thead>
        <tbody>
        @foreach (var t in thanhToans)
        {
            <tr>
                <td>@t.PhuongThucThanhToan</td>
                <td>@String.Format("{0:N0}", t.SoTien) đ</td>
                <td>@t.NgayThanhToan.ToString("dd/MM/yyyy HH:mm")</td>
                <td>@t.MaGiaoDich</td>
            </tr>
        }
        </tbody>
    </table>
}
<div class="mt-3">
    @if ((int)Model.VeTrangThai != 4 && (Model.ChuyenXe == null || Model.ChuyenXe.NgayKhoiHanh > DateTime.Now))
    {
        <form id="cancelForm" method="post" onsubmit="return cancelBooking();">
            <input type="hidden" name="id" value="@Model.VeId" />
            <div class="mb-2">
                <input type="text" name="lyDo" class="form-control" placeholder="Lý do hủy (tuỳ chọn)" />
            </div>
            <button type="submit" class="btn btn-danger">Huỷ vé</button>
        </form>
        <script>
            function cancelBooking() {
                if (!confirm('Bạn chắc chắn muốn huỷ vé này?')) return false;
                var form = document.getElementById('cancelForm');
                var data = new FormData(form);
                fetch('@Url.Action("AdminCancelBooking", "Booking")', {
                    method: 'POST',
                    body: data
                })
                .then(r => r.json())
                .then(res => {
                    alert(res.message);
                    if (res.success) window.location.reload();
                });
                return false;
            }
        </script>
    }
    else if ((int)Model.VeTrangThai == 4)
    {
        <div class="alert alert-danger">Vé đã bị huỷ.</div>
    }
</div>
<div class="mt-3">
    @if ((int)Model.VeTrangThai != 2 && (int)Model.VeTrangThai != 4)
    {
        <form id="confirmPaymentForm" method="post" onsubmit="return confirmPayment();">
            <input type="hidden" name="id" value="@Model.VeId" />
            <button type="submit" class="btn btn-success">✔ Xác nhận thanh toán</button>
        </form>
        <script>
            function confirmPayment() {
                if (!confirm('Xác nhận thanh toán cho vé này?')) return false;
                var form = document.getElementById('confirmPaymentForm');
                var data = new FormData(form);
                fetch('@Url.Action("AdminConfirmPayment", "Booking")', {
                    method: 'POST',
                    body: data
                })
                .then(r => r.json())
                .then(res => {
                    alert(res.message);
                    if (res.success) window.location.reload();
                });
                return false;
            }
        </script>
    }
    else if ((int)Model.VeTrangThai == 2)
    {
        <div class="alert alert-success">Vé đã được thanh toán.</div>
    }
</div>
<a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-secondary">Quay lại danh sách</a>

@* View này đã ngưng sử dụng. Vui lòng sử dụng Areas/Admin/Views/Booking/Detail.cshtml cho chức năng quản lý đặt vé admin. *@
