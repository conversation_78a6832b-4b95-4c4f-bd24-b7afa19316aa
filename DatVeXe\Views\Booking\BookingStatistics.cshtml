@using System.Text.Json
@{
    ViewBag.Title = "Thống kê đặt vé";
    var byDay = ViewBag.ByDay as IEnumerable<dynamic>;
    var byWeek = ViewBag.ByWeek as IEnumerable<dynamic>;
    var byMonth = ViewBag.ByMonth as IEnumerable<dynamic>;
    var topTrips = ViewBag.TopTrips as IEnumerable<dynamic>;
    var topSeats = ViewBag.TopSeats as IEnumerable<dynamic>;
    var cancelRate = ViewBag.CancelRate;
    var from = ((DateTime)ViewBag.From).ToString("yyyy-MM-dd");
    var to = ((DateTime)ViewBag.To).ToString("yyyy-MM-dd");
}
<h2>Thống kê đặt vé</h2>
<form method="get" class="row g-2 mb-3">
    <div class="col-md-2">
        <input type="date" name="from" class="form-control" value="@from" />
    </div>
    <div class="col-md-2">
        <input type="date" name="to" class="form-control" value="@to" />
    </div>
    <div class="col-md-2">
        <button type="submit" class="btn btn-primary">Lọc</button>
    </div>
</form>
<div class="row">
    <div class="col-md-6">
        <h5>Số vé đặt theo ngày</h5>
        <canvas id="chartDay"></canvas>
    </div>
    <div class="col-md-6">
        <h5>Số vé đặt theo tháng</h5>
        <canvas id="chartMonth"></canvas>
    </div>
</div>
<div class="row mt-4">
    <div class="col-md-6">
        <h5>Tỉ lệ hủy vé</h5>
        <div class="display-6">@String.Format("{0:0.##}", cancelRate) %</div>
    </div>
    <div class="col-md-6">
        <h5>Chuyến đông khách nhất</h5>
        <ul>
            @if (topTrips != null)
            {
                foreach (var t in topTrips)
                {
                    <li>Chuyến xe ID: @t.ChuyenXeId - @t.Count vé</li>
                }
            }
        </ul>
        <h5>Ghế được chọn nhiều nhất</h5>
        <ul>
            @if (topSeats != null)
            {
                foreach (var s in topSeats)
                {
                    <li>Ghế ID: @s.ChoNgoiId - @s.Count lần</li>
                }
            }
        </ul>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    var byDay = @Html.Raw(JsonSerializer.Serialize(byDay));
    var byMonth = @Html.Raw(JsonSerializer.Serialize(byMonth));
    new Chart(document.getElementById('chartDay'), {
        type: 'line',
        data: {
            labels: byDay.map(x => new Date(x.Date).toLocaleDateString()),
            datasets: [{ label: 'Số vé', data: byDay.map(x => x.Total), borderColor: 'blue', fill: false }]
        }
    });
    new Chart(document.getElementById('chartMonth'), {
        type: 'bar',
        data: {
            labels: byMonth.map(x => x.Month + '/' + x.Year),
            datasets: [{ label: 'Số vé', data: byMonth.map(x => x.Total), backgroundColor: 'orange' }]
        }
    });
</script>
@* View này đã ngưng sử dụng. Vui lòng sử dụng Areas/Admin/Views/Booking/Statistics.cshtml cho chức năng quản lý đặt vé admin. *@
