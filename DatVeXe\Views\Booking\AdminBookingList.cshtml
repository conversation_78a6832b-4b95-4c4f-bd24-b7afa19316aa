@model IEnumerable<dynamic>
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Quản lý đơn đặt vé";
    var chuyenXeList = ViewBag.ChuyenXeList as List<DatVeXe.Models.ChuyenXe>;
    var trangThaiVeList = ViewBag.TrangThaiVeList as List<DatVeXe.Models.TrangThaiVe>;
    string selectedChuyenXeId = ViewBag.SelectedChuyenXeId as string ?? "";
    string selectedNgayDi = ViewBag.SelectedNgayDi as string ?? "";
    string selectedTrangThaiVe = ViewBag.SelectedTrangThaiVe as string ?? "";
    string selectedTenKhach = ViewBag.SelectedTenKhach as string ?? "";
    
    var totalVe = Model != null ? Model.Count() : 0;
    var daThanhToanCount = Model != null ? Model.Count(v => (int)v.TrangThai == 2) : 0;
    var chuaThanhToanCount = Model != null ? Model.Count(v => (int)v.TrangThai == 1) : 0;
    var daHuyCount = Model != null ? Model.Count(v => (int)v.TrangThai == 4) : 0;
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-ticket-detailed me-2"></i>Quản lý đơn đặt vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item active">Quản lý đặt vé</li>
            </ol>
        </div>
        <div>
            @if (!string.IsNullOrEmpty(selectedChuyenXeId))
            {
                <a href="@Url.Action("ExportPassengerList", "Booking", new { chuyenXeId = selectedChuyenXeId })" class="btn btn-success">
                    <i class="bi bi-file-earmark-excel me-1"></i>Xuất Excel danh sách hành khách
                </a>
            }
        </div>
    </div>
    
    <!-- Dashboard Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Tổng đơn đặt vé</h6>
                            <h3 class="my-2">@totalVe</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="bi bi-arrow-up me-1"></i>@(totalVe > 0 ? "100%" : "0%")</span>
                                <span class="text-nowrap">Trạng thái hiện tại</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-primary d-flex align-items-center justify-content-center">
                            <i class="bi bi-ticket-detailed text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Đã thanh toán</h6>
                            <h3 class="my-2">@daThanhToanCount</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="bi bi-arrow-up me-1"></i>@(totalVe > 0 ? $"{daThanhToanCount * 100 / totalVe}%" : "0%")</span>
                                <span class="text-nowrap">Tỷ lệ thanh toán</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-success d-flex align-items-center justify-content-center">
                            <i class="bi bi-cash-coin text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Chưa thanh toán</h6>
                            <h3 class="my-2">@chuaThanhToanCount</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-warning me-2"><i class="bi bi-exclamation-triangle me-1"></i>@(totalVe > 0 ? $"{chuaThanhToanCount * 100 / totalVe}%" : "0%")</span>
                                <span class="text-nowrap">Cần xử lý</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-warning d-flex align-items-center justify-content-center">
                            <i class="bi bi-hourglass-split text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Đã hủy</h6>
                            <h3 class="my-2">@daHuyCount</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-danger me-2"><i class="bi bi-x-circle me-1"></i>@(totalVe > 0 ? $"{daHuyCount * 100 / totalVe}%" : "0%")</span>
                                <span class="text-nowrap">Tỷ lệ hủy vé</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-danger d-flex align-items-center justify-content-center">
                            <i class="bi bi-x-octagon text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <i class="bi bi-funnel me-1"></i> Bộ lọc tìm kiếm
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-3 col-sm-6">
                    <label class="form-label">Ngày đi</label>
                    <input type="date" name="ngayDi" class="form-control" value="@selectedNgayDi" />
                </div>
                <div class="col-md-3 col-sm-6">
                    <label class="form-label">Chuyến xe</label>
                    <select name="chuyenXeId" class="form-select">
                        <option value="">-- Chọn chuyến xe --</option>
                        @if (chuyenXeList != null)
                        {
                            foreach (var cx in chuyenXeList)
                            {
                                var tenTuyen = cx.TuyenDuong != null ? cx.TuyenDuong.TenTuyen : ($"{cx.DiemDi} - {cx.DiemDen}");
                                if (selectedChuyenXeId == cx.ChuyenXeId.ToString())
                                {
                                    <option value="@cx.ChuyenXeId" selected>@tenTuyen (@cx.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm"))</option>
                                }
                                else
                                {
                                    <option value="@cx.ChuyenXeId">@tenTuyen (@cx.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm"))</option>
                                }
                            }
                        }
                    </select>
                </div>
                <div class="col-md-2 col-sm-6">
                    <label class="form-label">Trạng thái vé</label>
                    <select name="trangThaiVe" class="form-select">
                        <option value="">-- Trạng thái vé --</option>
                        @if (trangThaiVeList != null)
                        {
                            foreach (var t in trangThaiVeList)
                            {
                                if (selectedTrangThaiVe == ((int)t).ToString())
                                {
                                    <option value="@((int)t)" selected>@t</option>
                                }
                                else
                                {
                                    <option value="@((int)t)">@t</option>
                                }
                            }
                        }
                    </select>
                </div>
                <div class="col-md-2 col-sm-6">
                    <label class="form-label">Tên khách</label>
                    <input type="text" name="tenKhach" class="form-control" value="@selectedTenKhach" placeholder="Tên khách" />
                </div>
                <div class="col-md-2 col-sm-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search me-1"></i>Tìm kiếm
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-ul me-1"></i>Danh sách đơn đặt vé</h5>
            <div class="d-flex align-items-center gap-2">
                <input type="text" id="searchTickets" class="form-control form-control-sm" placeholder="Tìm kiếm vé..." style="width: 200px;">
                <span class="badge bg-primary">@(Model != null ? Model.Count() : 0) đơn đặt vé</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="ticketsTable">
                    <thead class="table-light">
                        <tr>
                            <th>Mã vé</th>
                            <th>Tên khách</th>
                            <th>SĐT</th>
                            <th>Chuyến xe</th>
                            <th>Ghế</th>
                            <th>Trạng thái</th>
                            <th>Ngày đặt</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model != null && Model.Any())
                        {
                            foreach (var v in Model)
                            {
                                <tr data-search="@v.MaVe @v.TenKhach @v.SoDienThoai @v.ChuyenXe @v.Ghe">
                                    <td><span class="fw-medium">@v.MaVe</span></td>
                                    <td>@v.TenKhach</td>
                                    <td>@v.SoDienThoai</td>
                                    <td>@v.ChuyenXe</td>
                                    <td>@v.Ghe</td>
                                    <td>
                                        @{
                                            var ticketStatusClass = "";
                                            var ticketStatusText = "";
                                            var ticketStatusIcon = "";

                                            switch ((int)v.TrangThai)
                                            {
                                                case 1: // Đã đặt
                                                    ticketStatusClass = "bg-warning text-dark";
                                                    ticketStatusText = "Chưa thanh toán";
                                                    ticketStatusIcon = "bi-exclamation-circle";
                                                    break;
                                                case 2: // Đã thanh toán
                                                    ticketStatusClass = "bg-success";
                                                    ticketStatusText = "Đã thanh toán";
                                                    ticketStatusIcon = "bi-check-circle";
                                                    break;
                                                case 3: // Đã sử dụng
                                                    ticketStatusClass = "bg-info";
                                                    ticketStatusText = "Đã sử dụng";
                                                    ticketStatusIcon = "bi-person-check";
                                                    break;
                                                case 4: // Đã hủy
                                                    ticketStatusClass = "bg-danger";
                                                    ticketStatusText = "Đã hủy";
                                                    ticketStatusIcon = "bi-x-circle";
                                                    break;
                                                case 5: // Đã hoàn tiền
                                                    ticketStatusClass = "bg-secondary";
                                                    ticketStatusText = "Đã hoàn tiền";
                                                    ticketStatusIcon = "bi-arrow-return-left";
                                                    break;
                                                case 6: // Đã hoàn thành
                                                    ticketStatusClass = "bg-primary";
                                                    ticketStatusText = "Đã hoàn thành";
                                                    ticketStatusIcon = "bi-check-all";
                                                    break;
                                                default:
                                                    ticketStatusClass = "bg-warning text-dark";
                                                    ticketStatusText = "Chưa thanh toán";
                                                    ticketStatusIcon = "bi-exclamation-circle";
                                                    break;
                                            }
                                        }
                                        <span class="badge @ticketStatusClass"><i class="bi @ticketStatusIcon me-1"></i>@ticketStatusText</span>
                                    </td>
                                    <td>@(v.NgayDat.ToString("dd/MM/yyyy HH:mm"))</td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li><a class="dropdown-item" href="@Url.Action("AdminBookingDetail", "Booking", new { id = v.VeId })"><i class="bi bi-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="@Url.Action("PrintTicket", "Booking", new { id = v.VeId })" target="_blank"><i class="bi bi-printer me-2"></i>In vé</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@v.VeId, 'DaDon'); return false;"><i class="bi bi-check2-square me-2"></i>Đã đón</a></li>
                                                <li><a class="dropdown-item text-warning" href="#" onclick="updateTripStatus(@v.VeId, 'KhongCoMat'); return false;"><i class="bi bi-x-square me-2"></i>Không có mặt</a></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="updateTripStatus(@v.VeId, 'HuyChuyen'); return false;"><i class="bi bi-arrow-repeat me-2"></i>Huỷ chuyến</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item add-note" href="#" data-id="@v.VeId"><i class="bi bi-pencil me-2"></i>Thêm ghi chú</a></li>
                                                <li><a class="dropdown-item send-notification" href="#" data-id="@v.VeId" data-name="@v.TenKhach"><i class="bi bi-envelope me-2"></i>Gửi thông báo</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                                        <p class="mt-3 mb-0">Không có dữ liệu</p>
                                        <p class="text-muted small">Hãy thử thay đổi bộ lọc tìm kiếm</p>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal thêm ghi chú -->
<div class="modal fade" id="noteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm ghi chú</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="noteVeId" value="">
                <div class="mb-3">
                    <label for="noteContent" class="form-label">Ghi chú</label>
                    <textarea class="form-control" id="noteContent" rows="4" placeholder="Nhập ghi chú..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="saveNote">Lưu ghi chú</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gửi thông báo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="notificationVeId" value="">
                <div class="mb-3">
                    <label for="notificationSubject" class="form-label">Tiêu đề</label>
                    <input type="text" class="form-control" id="notificationSubject" placeholder="Tiêu đề email">
                </div>
                <div class="mb-3">
                    <label for="notificationMessage" class="form-label">Nội dung</label>
                    <textarea class="form-control" id="notificationMessage" rows="4" placeholder="Nội dung thông báo..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="sendNotification">Gửi thông báo</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking').addClass('active');
            
            // Xử lý tìm kiếm
            $('#searchTickets').on('keyup', function() {
                var value = $(this).val().toLowerCase();
                $("#ticketsTable tbody tr").filter(function() {
                    var searchData = $(this).data('search').toLowerCase();
                    $(this).toggle(searchData.indexOf(value) > -1);
                });
            });
            
            // Xử lý modal thêm ghi chú
            $('.add-note').click(function(e) {
                e.preventDefault();
                $('#noteVeId').val($(this).data('id'));
                $('#noteContent').val('');
                $('#noteModal').modal('show');
            });
            
            // Xử lý lưu ghi chú
            $('#saveNote').click(function() {
                var veId = $('#noteVeId').val();
                var note = $('#noteContent').val();
                
                $.ajax({
                    url: '@Url.Action("AddNoteToTicket", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        ghiChu: note
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('Đã cập nhật ghi chú', 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi cập nhật ghi chú', 'danger');
                    }
                });
                
                $('#noteModal').modal('hide');
            });
            
            // Xử lý modal gửi thông báo
            $('.send-notification').click(function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var name = $(this).data('name');
                
                // Get ticket info from server
                $.ajax({
                    url: '@Url.Action("GetTicketInfo", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#notificationVeId').val(veId);
                            $('#notificationSubject').val('Thông báo về vé xe của bạn');
                            $('#notificationMessage').val('Kính gửi ' + name + ',\n\nChúng tôi gửi thông báo này để...\n\nTrân trọng,\nNhà xe');
                            $('#notificationModal').modal('show');
                        } else {
                            showToast('Không thể gửi thông báo cho vé này: ' + response.message, 'warning');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi lấy thông tin vé', 'danger');
                    }
                });
            });
            
            // Xử lý gửi thông báo
            $('#sendNotification').click(function() {
                var veId = $('#notificationVeId').val();
                var subject = $('#notificationSubject').val();
                var message = $('#notificationMessage').val();
                
                $.ajax({
                    url: '@Url.Action("SendTicketNotification", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        subject: subject,
                        message: message
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('Đã gửi thông báo thành công', 'success');
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi gửi thông báo', 'danger');
                    }
                });
                
                $('#notificationModal').modal('hide');
            });
        });

        function updateTripStatus(veId, status) {
            let statusText = '';
            switch(status) {
                case 'DaDon': statusText = 'đã đón'; break;
                case 'KhongCoMat': statusText = 'không có mặt'; break;
                case 'HuyChuyen': statusText = 'huỷ chuyến'; break;
            }
            
            if (!confirm(`Xác nhận cập nhật trạng thái ${statusText} cho vé này?`)) return;
            
            fetch('@Url.Action("UpdateTripStatus", "Booking")', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `veId=${veId}&trangThai=${status}`
            })
            .then(r => r.json())
            .then(res => {
                if (res.success) {
                    showToast(res.message || 'Cập nhật trạng thái thành công', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast(res.message || 'Có lỗi xảy ra', 'danger');
                }
            })
            .catch(err => {
                showToast('Có lỗi xảy ra khi cập nhật trạng thái', 'danger');
                console.error(err);
            });
        }
        
        // Hiển thị thông báo toast
        function showToast(message, type) {
            var toast = $('<div class="toast align-items-center text-white bg-' + type + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">');
            toast.html(
                '<div class="d-flex">'+
                '  <div class="toast-body">' + message + '</div>'+
                '  <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>'+
                '</div>'
            );
            
            $('.toast-container').append(toast);
            var bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();
            
            // Xóa toast sau khi ẩn
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>
    
    <style>
        .avatar-sm {
            height: 3rem;
            width: 3rem;
            font-size: 1.25rem;
        }
        
        .badge {
            font-size: 0.85rem;
            padding: 0.5em 0.75em;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.85rem;
        }
        
        .card {
            border-radius: 0.5rem;
            overflow: hidden;
            border: none;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            border-bottom: 1px solid rgba(0,0,0,.125);
            padding: 0.75rem 1.25rem;
        }
        
        .table th, .table td {
            padding: 0.75rem 1rem;
            vertical-align: middle;
        }
        
        .table th {
            font-weight: 600;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0,0,0,.02);
        }
        
        .dropdown-item {
            padding: 0.5rem 1rem;
        }
        
        .dropdown-item i {
            width: 1rem;
            text-align: center;
        }
        
        #searchTickets {
            border-radius: 20px;
            padding-left: 2.5rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236c757d' class='bi bi-search' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: 10px center;
            background-size: 16px;
        }
    </style>
}
